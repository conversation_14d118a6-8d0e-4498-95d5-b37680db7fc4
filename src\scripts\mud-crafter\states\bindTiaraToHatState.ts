import { State, createState } from '../../../api/core/script/state'
import { createGeState } from '../../../api/script-utils/states/geStates'
import { Withdraw } from '../../../api/game/bank'
import { GeAction } from '../../../api/game/geAction'
import { ItemId } from '../../../data/itemId'
import { Equipment } from '../../../api/game/equipment'
import { Inventory } from '../../../api/game/inventory'
import { Bank } from '../../../api/game/bank'
import { Time } from '../../../api/utils/time'
import { log } from '../../../api/utils/utils'
import { Widgets } from '../../../api/game/widgets'
import { BindTiaraType, Runecraft } from '../../../api/game/runecraft'

export class BindTiaraToHat extends State {
    defaultState: () => State
    resupplyState: () => State
    tiaraType: BindTiaraType

    constructor(tiaraType: BindTiaraType, defaultState: () => State, resupplyState: () => State) {
        super()
        this.name = `Binding ${tiaraType.name.toLowerCase()} to hat`
        this.tiaraType = tiaraType
        this.defaultState = defaultState
        this.resupplyState = resupplyState
    }

    onAction(): void {
        // Check if tiara binding is already complete
        if (Runecraft.isRaimentsHatBoundToTiara(this.tiaraType)) {
            this.setState(this.defaultState())
            return
        }

        this.setState(this.prepareItems)
    }

    prepareItems = createState('Prepare Items (for binding)', () => {
        // Check if we have both items and binding is complete
        if (Inventory.contains(this.tiaraType.itemId) && Inventory.contains(ItemId.HAT_OF_THE_EYE)) {
            this.setState(this.bindTiara)
            return
        }

        // If HAT_OF_THE_EYE is equipped, unequip it first
        if (Equipment.isEquipped(ItemId.HAT_OF_THE_EYE)) {
            if (Inventory.getFreeSlots() < 1) {
                if (!Bank.openNearest() || !Bank.depositAll()) {
                    return
                }
            }
            Equipment.unequip(ItemId.HAT_OF_THE_EYE)
            return
        }

        if (!Bank.openNearest()) {
            return
        }

        // Withdraw the specified tiara
        if (!Withdraw.id(this.tiaraType.itemId, 1)
            .minimumAmount(1)
            .orState(this.geState)
            .ensureSpace()
            .withdraw()) {
            return
        }

        // Withdraw HAT_OF_THE_EYE
        if (!Withdraw.id(ItemId.HAT_OF_THE_EYE, 1)
            .minimumAmount(1)
            .orState(null)
            .ensureSpace()
            .withdraw()) {
            return
        }

        Widgets.closeTopInterface()

    })

    bindTiara = createState('Binding tiara to hat', () => {
        // Check if binding is already complete
        if (Runecraft.isRaimentsHatBoundToTiara(this.tiaraType)) {
            this.setState(this.defaultState())
            return
        }

        if (!Inventory.contains(this.tiaraType.itemId) || !Inventory.contains(ItemId.HAT_OF_THE_EYE)) {
            this.setState(this.prepareItems)
            return
        }

        // Use the specified tiara on HAT_OF_THE_EYE
        const tiara = Inventory.getById(this.tiaraType.itemId)
        if (tiara) {
            log(`Using ${this.tiaraType.name.toLowerCase()} on hat of the eye`)
            tiara.useOnItem(ItemId.HAT_OF_THE_EYE)

            // Wait for the action to complete
            if (Time.sleep(2000, 3000, () => !Inventory.contains(this.tiaraType.itemId) || !Inventory.contains(ItemId.HAT_OF_THE_EYE))) {
                this.setState(this.defaultState())
                return
            }
        }
    })

    get geState() {
        return createGeState(() => this.prepareItems, () => this.resupplyState(), [
            GeAction.item(this.tiaraType.itemId, 1).gePrice(1.1, 5000).buy()
        ], `GE State (for binding ${this.tiaraType.name.toLowerCase()} to hat)`)
    }
}
