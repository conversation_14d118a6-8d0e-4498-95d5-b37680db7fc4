import { State, createState } from '../../../api/core/script/state'
import { Bank } from '../../../api/game/bank'
import { GeAction } from '../../../api/game/geAction'
import { DefaultGeActionsState } from '../../../api/script-utils/states/geStates'
import { ItemId } from '../../../data/itemId'

export enum SpellbookType {
    LUNAR_SPELLBOOK = 'LUNAR_SPELLBOOK',
    NORMAL_SPELLBOOK = 'NORMAL_SPELLBOOK'
}

export class SwitchSpellbook extends State {
    spellbookType: SpellbookType

    constructor(spellbookType: SpellbookType) {
        super()
        this.spellbookType = spellbookType
    }

    onAction(): void {
        // Main logic will be implemented later
        this.setState(this.prepareItems)
    }

    prepareItems = createState('Prepare Items', () => {
        // TODO: Implement item preparation logic
    })

    walkToHouse = createState('Walk to House', () => {
        // TODO: Implement house walking logic
    })

    switchSpellbook = createState('Switch Spellbook', () => {
        // TODO: Implement spellbook switching logic
    })

    geState = new DefaultGeActionsState(
        () => this,
        this, // TODO: Replace with proper resupply state when available
        () => [
            // TODO: Add required items for spellbook switching
            GeAction.item(ItemId.TELEPORT_TO_HOUSE, 5).gePrice(1.1, 1000).buy(),
        ],
        () => {
            return true
        }
    )
}
