export const Offsetsx64 = {
    Game: {
        loginScreenId: 0xc573d0, //x      89 05 ? ? ? ? 48 8D 3D ? ? ? ? 48 8D 2D
        loginUsername: 0xc573b0, //x         55 41 57 41 56 53 48 81 EC ? ? ? ? 41 89 FE 4C 8D 3D ? ? ? ?
        loginPassword: 0xc57580, //x          55 41 57 41 56 53 48 81 EC ? ? ? ? 41 89 FE 4C 8D 3D ? ? ? ?
        engineSettings: 0x2043148, //x        FF 50 ? 48 8D 05 ? ? ? ? 48 C7 ? ? ? ? ?
        worldSelectorWorlds: 0xc574c0, //x    55 41 57 41 56 41 55 41 54 53 48 83 EC ? 39 F7
        clientFingerprint: 0xc56800, //x      ? ? ? ? 48 8D 7C 24 ? E8 ? ? ? ? 4C 8B BC 24 ? ? ? ?
        loginMessage1: 0xc57740, //x          55 41 57 41 56 53 48 81 EC ? ? ? ? 41 89 FE 89 F7
        loginMessage2: 0xc57758, //x          55 41 57 41 56 53 48 81 EC ? ? ? ? 41 89 FE 89 F7
        loginMessage3: 0xc57770, //x          55 41 57 41 56 53 48 81 EC ? ? ? ? 41 89 FE 89 F7
        client: 0xc573b0, //x                 48 8B 98 ? ? ? ? 48 8D B3 ? ? ? ? 48 8B BB ? ? ? ? 48 8B 07 FF 50 ? 8B 83 ? ? ? ? 83 F8 01 74 0B
        widgets: 0x129c428, //x              55 41 57 41 56 41 55 41 54 53 50 49 89 F4 49 89 FE 48 8B 4E ?
        config: 0x129c3b8, //x         C7 04 98 ? ? ? ? ? ? ? ? ? C7 04 98 ? ? ? ?
        itemContainers: 0xc33620, //x      ? ? ? ? ? 45 31 ED 48 8D 2D ? ? ? ? 4B 8B 1C EC

        renderTiles: 0x452770, //x
        onCollisionFlagsOr: 0x35ca30, //x
        onColliosnFlagsAnd: 0x35d120, //x
        onSoundEffect: 0x218100, //

        getTileHeight: 0x259a20, //x        55 41 57 41 56 41 54 53 31 C0
        getObjectComposition: 0x3906a0, //x   55 41 57 41 56 41 55 41 54 53 48 81 EC ? ? ? ? 89 FD
        setGameWorld: 0x254560, //x           41 57 41 56 41 55 41 54 53 48 83 EC ? 49 89 FE 4C 8D 25 ? ? ? ?
        onClientTick: 0x20e1a0, //x         75 08 41 83 86 ? ? ? ? ?
        onRenderWidgets: 0x28c820, //x        55 41 57 41 56 41 55 41 54 53 48 81 EC ? ? ? ? 44 89 CB 45 89 C6
        onMessageAdded: 0xe5990, //x         55 41 57 41 56 41 55 41 54 53 48 83 EC ? 4D 89 C6 49 89 CF 48 89 D3
        forceDisconnect: 0x2ad620, //x        41 57 41 56 41 55 41 54 53 48 83 EC ? 49 89 FE E8 ? ? ? ? 48 B9 DB 34 B6 D7 82 DE 1B 43
        onHardwareDataRequested: 0x823030, //x 53 48 89 FB 83 FE 16
        addHealthBar: 0x2b8210, //x 55 41 57 41 56 41 55 41 54 53 48 83 EC ? 44 89 4C 24 ? 44 89 44 24 ? 41 89 CC 89 54 24 ? 49 89 FF 48 63 F6 48 8D 7C 24 ? E8 ? ? ? ? 4D 8D B7 ? ? ? ? 49 8B 87 ? ? ? ?

        invokeMenuActionHook: 0x298a20, //x   child function || 41 56 4C 8B 64 24 ? 41 54 4C 8B 7C 24 ? 41 57 41 52 E8 ? ? ? ? 48 83 C4 ? 8D 8B ? ? ? ?
        writeJagString: 0xf1650, //x         48 0F BE 47 ? 48 85 C0 78 2C
    },

    Client: {
        menu: 0x6f5f8, //x                    E8 ? ? ? ? 48 83 C4 ? 48 8B BC 24 ? ? ? ?
        plane: 0x6b30, //x                    89 8F ? ? ? ? 66 83 F8 FF 0F 84 01 43 00 00
        currentWorld: 0x6ff34, //x            8B 90 ? ? ? ? 44 8B 47 ?
        gameState: 0x2130, //x                55 41 57 41 56 41 55 41 54 53 48 81 EC ? ? ? ? 8B 9F ? ? ? ?
        loggedInCycle: 0x451504, //x          74 30 8B 4F ?
        cycle: 0x2154, //x                    45 01 F0 41 83 F8 02
        serverTick: 0x2154 + 0x4, //x         45 01 F0 41 83 F8 02
        skillCurrentLevels: 0x6f434, //x       44 8B B4 81 ? ? ? ?
        skillRealLevels: 0x6f498, //x          44 8B B4 81 ? ? ? ?
        skillExperiences: 0x6f4fc, //x         44 8B B4 81 ? ? ? ?
        localPlayer: 0x6f3c8, //x             48 8B B0 ? ? ? ? 48 85 F6 74 46
        worldView: 0x6b18, //x                FF 50 ? 41 89 C6 48 8B 45 00 48 89 EF FF 50 ? 89 C3 48 8B 45 00 48 89 EF FF 50 ?
        sceneTilesData: 0x6b28, //x           49 8B AC 24 ? ? ? ? 48 8B 83 ? ? ? ? 48 81 C3 ? ? ? ? 48 89 DF FF 50 ?
        scene: 0x70a10, //x                   49 8B BD ? ? ? ? 4C 89 F6 E8 ? ? ? ?
        destinationTile: 0x2008, //x             48 8B 87 ? ? ? ? C7 40 0C FF FF FF FF
        grandExchangeOffers: 0x6f7b0, //x       55 41 57 41 56 41 54 53 48 81 EC ? ? ? ? 41 BC ? ? ? ?
        hintArrowsDataContainer: 0x6ffb0, //x  rev 227: 00250346 | rev 229: 00286eb7 | 49 8B AC 24 ? ? ? ? 4D 8B BC 24 ? ? ? ? 4C 39 FD 0F 28 44 24 ?
        cameraX: 0x6f8d8, //x                   55 41 57 41 56 53 50 41 89 CE 48 89 FB
        cameraZ: 0x6f8dc, //x                    55 41 57 41 56 53 50 41 89 CE 48 89 FB
        cameraY: 0x6f8e0, //x                   55 41 57 41 56 53 50 41 89 CE 48 89 FB
        cameraPitch: 0x6f8e4, //x             55 41 57 41 56 53 50 41 89 CE 48 89 FB
        cameraYaw: 0x6f8e8, //x                 55 41 57 41 56 53 50 41 89 CE 48 89 FB
        cameraZoom: 0x6f790, //x           55 41 57 41 56 53 50 41 89 CE 48 89 FB
        playableAreaX: 0x1fdc, //x
        playableAreaY: 0x1fe0, //x


        // rootWidgetXs: 0x7a2dc,
        // rootWidgetYs: 0x7a46c,
        // rootWidgetWidths: 0x7a5fc,
        // rootWidgetHeights: 0x7a1a8,
        // rootWidgetCount: 0x7a1a8
    },

    HintArrowsDataContainer: {
        instance: 0x0,
    },

    HintArrowsDataContainerInstance: {
        hintArrowType: 0x0,
        hintArrowNpcIndex: 0x4,
        hintArrowX: 0x10,
        hintArrowY: 0x14,
    },

    WorldView: {
        sceneCoords: 0x10, //
        npcsHashMap: 0xb8, //x           CHILD METHOD | 74 1D 4C 89 FA 5B 41 5C 41 5E 41 5F 5D E9 ? ? ? ?
        playersHashMap: 0x88, //x        229.2 x86-84: 0022db49 |  4A 63 8C A8 ? ? ? ? 49 8B B7 ? ? ? ? 49 8B BF ? ? ? ?
    },

    Scene: {
        baseX: 0x54bc,
        baseY: 0x54bc + 0x4,
        isInstanced: 0x54dd, //x            89 83 ? ? ? ? 8D 0C C5 ? ? ? ? 48 C1 E8 ? 66 0F 3A 22 C0 01 66 0F 70 C0 50
    },
    SceneTile: {
        wallDecorationObject: 0x100, //
        groundObject: 0x110, //
        groundItems: 0xb0, //x      49 8B 45 00 48 8B 88 ? ? ? ?
        standardGameObject: 0x60, //
        wallObject: 0xf0, //x  AKA BOUNDARY OBJECT | 48 8B 40 ? 44 0F AF B8 ? ? ? ? 45 01 E7 44 0F AF B8 ? ? ? ? 45 01 EF 49 63 CF 48 8B 80 ? ? ? ? 48 C1 E1 ? 48 8B 44 08 ?
    },
    SceneTilesContainer: {
        tileAtZarr: 0x51f8, //x    48 8B 44 01 ? 48 85 C0 74 07 44 89 80 ? ? ? ?
        tileAtXarr: 0x51e0, //x    48 8B 44 01 ? 48 85 C0 74 07 44 89 80 ? ? ? ?
        tileAtYarr: 0x51e4, //x    48 8B 44 01 ? 48 85 C0 74 07 44 89 80 ? ? ? ?

        collisionFlags: 0x68, //x    4C 8B 40 ? 48 63 83 ? ? ? ? 48 8D 0C 80
    },

    SceneTilesData: {
        sceneTilesContainer: 0x10,
    },

    SceneCoords: {
        selectedX: 0x26c, //x              AND PARENT METHOD | 0F 29 4F ? 0F 28 4E ? 0F 29 4F ? 0F 28 4E ? 0F 29 4F ?
        selectedY: 0x270, //x              AND PARENT METHOD | 74 4C 48 8D 05 ? ? ? ? 8B 8C 24 ? ? ? ?
    },

    Actor: {
        rotation: 0x28,
        isMoving: 0x1b0 - 0x4,
        interactingIndex: 0x140,
        localX: 0x1ac, //x              229.2: 002b4af0 |   48 8B B6 ? ? ? ? 8B BE ? ? ? ?
        localY: 0x1d4, //x
        animation: 0x16c,
        animationFrame: 0x16c + 0x4,
        isRunningSpotanim: 0x2c,
        spotAnimation: 0x3a8,
        renderX: 0x20,
        renderY: 0x24,
    },
    SpotAnimation: {
        id: 0x4,
        frame: 0x14,
    },

    GroundObject: {
        tag: 0x40,
    },
    WallDecorationObject: {
        tag: 0x60,
    },
    StandardGameObject: {
        tag: 0x70,
    },
    WallObject: {
        tag: 0x58,
    },

    EngineSettings: {
        fpsCap: 0x185e0,
    },

    GrandExchangeOffer: {
        status: 0x0,
        itemId: 0x04,
        amount: 0x0c,
        price: 0x08,
        isBuying: 0x0c,
    },
    GroundItem: {
        id: 0x18,
        amount: 0x1c,
    },
    RsItemContainer: {
        id: 0x0,
        itemIds: 0x8,
        itemAmounts: 0x20,
    },
    Menu: {
        isOpen: 0x3a0, //x          GO TO METHOD INSIDE | E8 ? ? ? ? 48 83 C4 ? 48 8B BC 24 ? ? ? ?
    },
    Npc: {
        composite: 0x3e0,
        index: 0x3f0,
        renderCycle: 0x34,
    },
    Player: {
        username: 0x3d0, //x             48 8B B0 ? ? ? ? 48 85 F6 74 46
        index: 0x488, //x               rev 227: liblibs.hal.system.osclient.so+222CF5:
        headIconPrayer: 0x468, //TODO       CHYBA NIEBARDZO | 8B 85 ? ? ? ? 83 F8 FF
        headIconSkull: 0x3e8, //        227: liblibs.hal.system.osclient.so+251C07:
        combatLevel: 0x438, //x          rev. 227 liblibs.hal.system.osclient.so+161855:
    },

    NpcComposite: {
        id: 0x20,
    },

    Username: {
        value: 0x0,
    },
    Widget: {
        id: 0x24,
        index: 0x28,
        parentId: 0x94,
        cycle: 0x58,

        contentType: 0x50,
        text: 0x170,
        itemId: 0x780 + 0x20, //?
        width: 0x74,
        height: 0x78,
        relativeX: 0x7c,
        relativeY: 0x80,
        isHidden: 0x98,
        textureId: 0xe0,
        isInput: 0x578,
        itemAmount: 0x784 + 0x20, //?
        children: 0x7a0 + 0x10, //              55 41 57 41 56 41 54 53 89 F5 48 8D 1D ? ? ? ?

    },

    ClientFingerprint: {
        libArchitecture: 0x0, //ARM64 or x86_64
        processorArchitecture: 0x18, //64 or (idk for arm)
        clientType: 0x50, //NXT-Android
        deviceInfo: 0x68,
    },
    DeviceInfo: {
        deviceName: 0x0, //Samsung / SM-G9500 (SM-G9500 / SM-G9500) (Android 12)
    },

    Native: {
        AInputEvent_getType: 0x00086850,
        AInputEvent_getDeviceId: 0x000877f0,

        AKeyEvent_getKeyCode: 0x00086840,
        AKeyEvent_getAction: 0x000877d0,
        AKeyEvent_getMetaState: 0x000877e0,

        AMotionEvent_getAction: 0x00095270,
        AMotionEvent_getPointerId: 0x000957f0,
        AMotionEvent_getX: 0x00094e20,
        AMotionEvent_getY: 0x00095200,
        AMotionEvent_getButtonState: 0x00094bf0,
        AMotionEvent_getPointerCount: 0x000943e0,
        AMotionEvent_getToolType: 0x00095540,
        AMotionEvent_getAxisValue: 0x000944b0,

        onInputEvent: 0xd8700,

        keyEvent_Action: 0x38,
        keyEvent_Flags: 0x3c,
        keyEvent_KeyCode: 0x40,
        keyEvent_ScanCode: 0x44,
        keyEvent_MetaState: 0x48,
        keyEvent_DownTime: 0x50,
        keyEvent_EventTime: 0x58,

        inputEvent_DeviceId: 0x4c,
        inputEvent_Type: 0x10,
        keyEvent_RepeatCount: 0x4c,
        inputEvent_Source: 0x10,
    },
}
