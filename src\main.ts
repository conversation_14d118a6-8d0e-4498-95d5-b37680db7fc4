import { getProfile } from './accountProfile'
import { AccountData } from './api/model/accountData'
import { Time } from './api/utils/time'
import { createOnClickListener, killCurrentProcess, log, runOnUtilityThread, startLoop, startNewThread } from './api/utils/utils'
import { WebUtils } from './api/utils/webUtils'
import { Client } from './api/wrappers/client'
import { Game } from './api/wrappers/game'
import { BotSettings } from './botSettings'
import { handleConsoleCommand } from './consoleHandler'
import { FileAccessWatcher } from './core/background-tasks/fileAccessWatcher'
import { FingerprintSpoofer } from './core/background-tasks/fingerprintSpoofer'
import { ScriptStarter } from './core/background-tasks/scriptStarter'
import { Bot } from './core/bot'
import { CanvasHandler } from './core/canvasHandler'
import { ProxyHandler } from './core/proxyHandler'
import { injectSentry } from './core/sentry'
import { Threading } from './core/threading'
import { Ui } from './core/ui'
import { checkForUpdates } from './core/updater'

function applyDevMode() {
    log('Loading account in dev mode')
    BotSettings.autostartEnabled = false
    BotSettings.devMode = true
    BotSettings.useProxy = false
    BotSettings.useBehavioralPointGeneration = true
    BotSettings.blockProfilingFileAccess = false
    BotSettings.spoofSystem = false
    BotSettings.isRealDevice = true

    setTimeout(() => {
        startNewThread(() => {
            // AccountData.current = WebUtils.getTyped<AccountData>('accounts/get?username=<EMAIL>') gdk
            AccountData.current = WebUtils.getTyped<AccountData>('accounts/get?username=<EMAIL>')
            BotSettings.devModeScript = 'MudRunes'

            log('Loaded account in dev mode: ', JSON.stringify(AccountData.current))
        }, 'FetchAccount')
    }, 3000)
}


function initAccount() {
    setTimeout(() => {
        startNewThread(() => {
            AccountData.current = WebUtils.getTyped<AccountData>(`accounts/get?username=${BotSettings.selectedAccount}`)
            log('Using account: ', JSON.stringify(AccountData.current))
        }, 'FetchAccount')
    }, 2500)
}

function handleCommand(message: any) {
    const command = message.payload
    recv('onCommand', handleCommand)
    log('onCommand: ', JSON.stringify(message))
    handleConsoleCommand(command)
}

function load(stage: string) {
    log('Launching OSRSBot')

    Java.perform(function () {
        const Build = Java.use('android.os.Build')
        BotSettings.isRealDevice = Build.MODEL.value.includes('STK-L21') || Build.MODEL.value.includes('2412DPC0AG')|| Build.MODEL.value.includes('2412DPC0AG')
    })

    Time.sleep(50)

    if (BotSettings.isRealDevice) {
        applyDevMode()
    } else {
        BotSettings.selectedAccount = ScriptStarter.getAccountFromStartArguments()
        initAccount()
    }




    setTimeout(() => {
        if (Client.gameState <= 5 || Client.gameState == 1000) {
            killCurrentProcess('Game did not initialize within 120 seconds.  ')
        }
    }, 180_000)

    recv('applyDevMode', applyDevMode)
    recv('onCommand', handleCommand)

    setTimeout(() => {
        log('frida launching new thread ')
        startNewThread(() => {
            log(' frida Started some new thread...')

            CanvasHandler.drawLogs = true
            log('Waiting for game to initialize.')
            const start = Time.now()

            if(stage == "early" && !BotSettings.devMode) {
                checkForUpdates()
            }

            const interval = startLoop(
                () => {
                    try {
                        if (Game.base == null || Game.base.isNull() || Client.gameState < 10) {
                            return
                        }

                        log('Game loaded within  ', Time.now() - start)

                            try {
                            Bot.initData()
                            Bot.init()

                            CanvasHandler.drawLogs = false

                            } catch (e1) {
                                log('main.ts #1, ', e1, e1.stack)
                                killCurrentProcess('Bot initialization failed.', true)
                        
                            }
                        interval.interrupt()
                    } catch (e) {
                        log('main.ts #2, ', e, e.stack)

                        if (JSON.stringify(e).includes('already replaced this function')) {
                            ProxyHandler.setNeutralProxy()
                            killCurrentProcess('Error injecting java methods.')
                        }
                    }
                },
                1000,
                'initializer'
            )
        }, 'Initializer')
    }, 10)

    setTimeout(() => {
        startNewThread(() => {
            log('Initializing UI')
            Ui.buildButtons()

            if(BotSettings.devMode) {
                Ui.buildCanvas()
            }
            
        }, 'UIInitializer')
    }, 3000)
}

rpc.exports = {
    async init(stage, parameters) {
       log('Init?: ', JSON.stringify(stage), JSON.stringify(parameters))

       setTimeout(() => {
           load(stage)
       }, 50)

       injectGameLoadWaiter()

       Time.sleep(3000)
        return true
    },
    dispose() {
        log('Stopping java threads 10')
        Threading.stopAllJavaThreads()
        log('Stopped java threads')

        Time.sleep(2000)
        Ui.removeCreatedUiElements()
    },
}


function injectGameLoadWaiter() {
    Java.perform(() => {
        // Intercept com.jagex.android.b class
        const JagexAndroidB = Java.use('com.jagex.android.b')

        // Intercept a() method
        JagexAndroidB.a.implementation = function () {
            // Add sleep at the beginning of the method
            log('Intercepted com.jagex.android.b.a() - adding sleep(1000)')
            const JavaThread = Java.use('java.lang.Thread')

            while (AccountData.current == null) {
                try {
                    log("Waiting for account. ")
                    JavaThread.sleep(1000)
                } catch (e) {
                    log('Error in sleep: ', e)
                }
            }

            log('Wait end. Process full load of the game now. Using account: ', JSON.stringify(AccountData.current))
            // Call the original method

            FingerprintSpoofer.initJava()
            FileAccessWatcher.init()

            JavaThread.sleep(1000)

            return this.a()
        }

        log('Successfully intercepted com.jagex.android.b.a()')
    })
}

