import { State, createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { GeAction } from '../../../api/game/geAction'
import { Varps } from '../../../api/game/varps'

import { ItemId } from '../../../data/itemId'
import { MudCrafter } from '../mudCrafter'
import { GetRingOfTheElementsState } from './mudCrafterGe'

export class MudCrafterGeBetter extends State {
    onAction(): void {
        const actions = [
            GeAction.item(ItemId.MUD_RUNE, 20000).gePrice(0.95, -5).sell(),

            GeAction.item(ItemId.PURE_ESSENCE, 8000).gePrice(1.12, 5).buy(),
            GeAction.item(ItemId.BINDING_NECKLACE, 40).gePrice(1.12, 200).buy(),
            GeAction.item(ItemId.EARTH_TIARA, 1).gePrice(1.12, 1555).withEq().buy(),
            GeAction.item(ItemId.RING_OF_DUELING8, 40).gePrice(1.12, 1000).buy(),
            GeAction.item(ItemId.EARTH_TALISMAN, 300).gePrice(1.12, 350).buy(),
            GeAction.item(ItemId.EARTH_RUNE, 8000).gePrice(1.12, 5).buy(),
            GeAction.item(ItemId.RING_OF_WEALTH_5, 3).gePrice(1.12, 3000).buy(),

            GeAction.item(ItemId.ASTRAL_RUNE, 300).continueWhenAmount(150).gePrice(1.12, 50).buy(),
            GeAction.item(ItemId.COSMIC_RUNE, 300).continueWhenAmount(150).gePrice(1.12, 50).buy(),
            GeAction.item(ItemId.AIR_RUNE, 5300).continueWhenAmount(150).gePrice(1.12, 5).buy(),
            GeAction.item(ItemId.STEAM_BATTLESTAFF, 1).gePrice(1.12, 5000).buy(),
        ]

        if (!GeAction.executeAll(actions, () => MudCrafter.resupply)) {
            return
        }

        if (!Bank.openNearest() || !Bank.depositAll()) {
            return
        }

        if (Varps.ringOfElementsCharges < 50 || !Bank.contains(ItemId.RING_OF_THE_ELEMENTS_26818)) {
            this.setState(new GetRingOfTheElementsState(this, MudCrafter.resupply))
            return
        }

       if (Bank.get().countByIds(995) > 3_000_000 || Bank.get().countByIds(ItemId.MUD_RUNE) > 2000) {
           this.setState(MudCrafter.giveToMule)
           return
       }

        this.setDefaultState()
    }
}


