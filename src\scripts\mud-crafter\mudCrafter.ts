import { StatefulScript } from '../../api/core/script/statefulScript'
import { Skill } from '../../api/game/skill'
import { Varps } from '../../api/game/varps'
import { Item } from '../../api/model/item'
import { TradePackage } from '../../api/model/tradePackage'
import { GiveToMuleState } from '../../api/script-utils/mule/giveMuleStrategy'
import { ResupplyMuleState } from '../../api/script-utils/mule/resupplyMuleStrategy'
import { hourRatio } from '../../api/utils/utils'
import { BotSettings } from '../../botSettings'
import { ItemId } from '../../data/itemId'
import { MuleReceiver } from '../muling/muleReceiver'
import { MudCrafterMain } from './states/mudCrafterMain'
import { MudCrafterMainBetter } from './states/mudCrafterMainBetter'

export class MudCrafter extends StatefulScript {
    static resupply = new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), 308)
    static giveToMule = new GiveToMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_GIVE), BotSettings.tradeWorldP2p,
     [new Item(995, 1_000_000),   new Item(ItemId.COSMIC_RUNE, 300),  new Item(ItemId.ASTRAL_RUNE, 300),  new Item(ItemId.AIR_RUNE, 1000)], 

    [new Item(995, 50_000_000), new Item(ItemId.MUD_RUNE, *********), new Item(ItemId.BLOOD_RUNE, *********), new Item(ItemId.DEATH_RUNE, *********),
         new Item(ItemId.COSMIC_RUNE, *********),  new Item(ItemId.ASTRAL_RUNE, *********),  new Item(ItemId.AIR_RUNE, *********),
         
         new Item(ItemId.SOUL_RUNE, *********)]

)

    static tripsDone = 0

    constructor() {
        super()
        this.loopInterval = 50
    }

    onStart(): void {
        this.initWithState(new MudCrafterMainBetter())
        MudCrafter.tripsDone = 0
    }

    onDraw(canvas: any, paint: any): void {
        this.drawText(`Mud runes (${this.currentState?.name})`)
        this.drawText('Runtime: ' + this.progressTracker.getTimeRunning())
        this.drawText(this.progressTracker.getExpGainedString(Skill.RUNECRAFTING))
        this.drawText('Trips: ' + this.trips() + ` (${hourRatio(this.progressTracker.startTime, this.trips())})`)
        this.drawText(`Pouch: ${Varps.getVarbit(13682)}`)
    }

    trips() {
        return MudCrafter.tripsDone
    }

    runtimeInfo(): string {
        return `<th>RC: ${Skill.RUNECRAFTING.getCurrentLevel()}</th> <th>Trips: ${this.trips()} (${hourRatio(this.progressTracker.startTime, this.trips())})</th>  <th>${this.currentState?.name}</th>`
    }
}
