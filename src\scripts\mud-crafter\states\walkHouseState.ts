import randomItem from 'random-item'
import { State, createState } from '../../../api/core/script/state'
import { GameObjects } from '../../../api/game/gameObjects'
import { HouseAd, HouseAdvertisement } from '../../../api/game/houseAdvertisement'
import { Walking } from '../../../api/game/walking'
import { Tile } from '../../../api/model/tile'
import { Time } from '../../../api/utils/time'
import { log } from '../../../api/utils/utils'
import { Client } from '../../../api/wrappers/client'
import { WorldHopping } from '../../../api/game/worldHopping'

export class WalkHouseState extends State {
    currentHouse: HouseAd = null
    bannedHouses: string[] = ['Pyramid']
    targetState: () => State
    houseFilter?: (house: HouseAd) => boolean

    constructor(targetState: () => State, houseFilter?: (house: HouseAd) => boolean) {
        super()
        this.targetState = targetState
        this.houseFilter = houseFilter
    }

    onGameMessage(username: string, message: string): void {
        if (message.includes('That player is offline')) {
            this.currentHouse = null
        }
        if (message.includes('visited anyone this session')) {
            this.currentHouse = null
        }
    }

    onAction(): void {
        if (this.isAtHouse()) {
            this.setState(this.targetState())
            return
        }

        if(!WorldHopping.switchToWorld(330)) {
            return
        }

        this.goToHouse()
    }

    goToHouse() {
        if (!Walking.walkTo(new Tile(2952, 3220, 0), 3)) {
            return
        }

        if (this.currentHouse != null) {
            GameObjects.getById(29091)?.click(5)
            Time.sleep(() => this.isAtHouse())
            return
        }

        if (HouseAdvertisement.isOpen()) {
            this.selectHouse()
            return
        }

        GameObjects.getById(29091)?.click(3)

        if (Time.sleep(() => HouseAdvertisement.isOpen())) {
            Time.sleepCycles(7)
            this.selectHouse()
        }
    }

    selectHouse() {
        if (this.currentHouse == null) {
            let availableHosts = HouseAdvertisement.getAvailableHosts()
                .filter((h) => !this.bannedHouses.find((bannedHouse) => bannedHouse.toLowerCase().includes(h.playerName.toLowerCase())))
                .filter((h) => h.location.includes('RIM') && h.constructionLevel >= 85)

            // Apply custom filter if provided
            if (this.houseFilter) {
                availableHosts = availableHosts.filter(this.houseFilter)
            } else {
                // Default filter for prayer script compatibility
                availableHosts = availableHosts.filter((h) => h.hasGildedAltar && h.jeweleryBoxes >= 2)
            }

            const host = randomItem(availableHosts)

            log('Selecting host ', JSON.stringify(host))
            this.currentHouse = host
        }

        this.currentHouse = HouseAdvertisement.getAvailableHosts().find((h) => h.playerName == this.currentHouse?.playerName)

        if (this.currentHouse) {
            HouseAdvertisement.select(this.currentHouse)
            Time.sleep(() => this.isAtHouse() || this.currentHouse == null)
        }
    }

    leaveHouse() {
        const exitPortal = GameObjects.getById(4525)
        if (!exitPortal) {
            this.bannedHouses.push(this.currentHouse?.playerName)
            this.currentHouse = null
            Walking.walkTo(new Tile(2952, 3220, 0), 3)
            return
        }

        GameObjects.getById(4525).click(3)
        Time.sleep(() => !this.isAtHouse())
    }

    isAtHouse() {
        return Client.scene.isInstancedArea
    }
}
