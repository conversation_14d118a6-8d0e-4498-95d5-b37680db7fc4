import { ItemId } from '../../data/itemId'
import { Varps } from './varps'

export type BindTiaraType = {
    name: string
    varbitId: number
    itemId: number
}

export const BindTiaraTypes = {
    WATER_TIARA: {
        name: 'WATER_TIARA',
        varbitId: 2, // TODO: Find actual varbit ID for water tiara binding completion
        itemId: ItemId.WATER_TIARA
    },
    EARTH_TIARA: {
        name: 'EARTH_TIARA',
        varbitId: 3, // TODO: Find actual varbit ID for earth tiara binding completion
        itemId: ItemId.EARTH_TIARA
    },
}

export class Runecraft {
   
    static isRaimentsHatBoundToTiara(tiaraType: BindTiaraType): boolean {
        return Varps.getVarbit(13709) == tiaraType.varbitId
    }

}
