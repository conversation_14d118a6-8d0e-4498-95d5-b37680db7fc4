import { State, createState } from '../../../api/core/script/state'
import { createGeState, DefaultGeActionsState } from '../../../api/script-utils/states/geStates'
import { WalkHouseState } from './walkHouseState'
import { Withdraw } from '../../../api/game/bank'
import { ItemPredicate } from '../../../data/itemPredicates'
import { GeAction } from '../../../api/game/geAction'
import { ItemId } from '../../../data/itemId'
import { Equipment } from '../../../api/game/equipment'
import { GameObjects } from '../../../api/game/gameObjects'
import { log } from '../../../api/utils/utils'
import { Varps } from '../../../api/game/varps'
import { Time } from '../../../api/utils/time'
import { Walking } from '../../../api/game/walking'
import { Magic, SpellbookType } from '../../../api/game/magic'



export class SwitchSpellbook extends State {
    spellbookType: SpellbookType
    walkHouseState = new WalkHouseState(() => this.switchSpellbook)
    resupplyState: () => State
    defaultState: () => State

    constructor(spellbookType: SpellbookType, defaultState: () => State, resupplyState: () => State) {
        super()
        this.name = "Switching spellbook: " + spellbookType
        this.spellbookType = spellbookType
        this.resupplyState = resupplyState
        this.defaultState = defaultState
    }

    onAction(): void {
        this.setState(this.prepareItems)
    }

    onBackgroundAction(): void {
        Walking.setRunAuto()


    }

    onDraw(g: any): void {
        this.drawText("Current spellbook: " + Magic.currentSpellbook.name)
    }

    prepareItems = createState('Prepare Items (for spellbook)', () => {
        if (!Equipment.withdrawAndEquipByPredicate(ItemPredicate.ringOfWealth, () => this.geState)) {
            return
        }

        if (!Withdraw.all(
            this.geState,
            Withdraw.predicate(ItemPredicate.skillsNecklace, 1).ensureSpace()
        )) {
            return
        }

        this.setState(this.walkHouseState)
    })

    switchSpellbook = createState('Switch Spellbook - at house', () => {
        const altar = GameObjects.getById(29150)

        if (Magic.currentSpellbook == this.spellbookType) {
            this.setState(this.defaultState())
            return
        }

        if (!altar) {
            log("No altar found")
            return
        }

        altar.click(this.spellbookType.occultAltarIndex)
        Time.sleep(() => Magic.currentSpellbook == this.spellbookType)


    })

    geState = createGeState(() => this.prepareItems, () => this.resupplyState(), [
        GeAction.item(ItemId.SKILLS_NECKLACE6, 1).gePrice(1.1, 1000).buy(),
        GeAction.item(ItemId.RING_OF_WEALTH_5, 1).gePrice(1.1, 1000).buy(),
    ], "GE State (for spellbook switching)")
}