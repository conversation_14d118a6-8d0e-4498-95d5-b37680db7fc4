export const OffsetsArm = {
    Game: {
        loginUsername: 0xa80fc0, //x  ? ? ? ? ? ? ? ? 01 01 80 52 62 00 80 52 ? ? ? ? ? ? ? ?
        loginPassword: 0xa81508, //x  ? ? ? ? ? ? ? ? 01 01 80 52 62 00 80 52 ? ? ? ? ? ? ? ?
        engineSettings: 0x1e71db0, //x ? ? ? ? ? ? ? ? 14 00 40 F9 F4 00 00 B5
        worldSelectorWorlds: 0xa81000, //x  CC FD FF 54 ? ? ? ? ? ? ? ? 0B 04 00 51 F4 03 15 2A
        clientFingerprint: 0xa80490, //x A9 F3 15 38 AC 83 14 B8 0A 31 00 B8 BF F3 14 38 ? ? ? ?
        loginScreenId: 0xa81124, //x   00 01 3F D6 08 04 40 F9 13 00 80 12 09 09 40 F9
        loginMessage1: 0xa815f8, //x 00 01 3F D6 08 04 40 F9 13 00 80 12 09 09 40 F9
        loginMessage2: 0xa81610, //x 00 01 3F D6 08 04 40 F9 13 00 80 12 09 09 40 F9
        loginMessage3: 0x81628, //x 00 01 3F D6 08 04 40 F9 13 00 80 12 09 09 40 F9
        client: 0xa80fb0, //x  60 82 00 AD ? ? ? ? E0 03 16 AA 7F 1A 00 F9 18 C1 26 91 60 02 00 FD ? ? ? ?
        widgets: 0x10cfb18, //x ? ? ? ? 08 03 80 52 7B 3F 40 92
        config: 0x10cfad0, //x C8 00 00 B4 ? ? ? ? 89 F6 7E D3 1F 69 29 B8 ? ? ? ? 1F 69 29 B8
        itemContainers: 0xa5d328, //x 36 79 6A F8 56 01 00 B4

        renderTiles: 0x399f10, //x  4A 3D 40 B9 6B 01 0A 4B 8A 01 0A 4B 6B 01 08 0B 48 01 08 0B
        onCollisionFlagsOr: 0x2cd970, //TODO NOT UPDATED SINCE 229-1
        onColliosnFlagsAnd: 0x2ce184, //TODO  NOT UPDATED SINCE 229-1
        onSoundEffect: 0x218100,//x

        getTileHeight: 0x20b99c, //x                 40 43 04 B9 59 37 00 B9 48 17 01 F9 > child method first one
        getObjectComposition: 0x2fdc48, //x         48 79 68 F8 08 04 00 B4
        setGameWorld: 0x205d0c, //x             ? ? ? ? ? ? ? ? 35 00 00 12
        onClientTick: 0x1cdd14, //x             88 00 00 35 68 56 61 B9 08 05 00 11 68 56 21 B9
        onRenderWidgets: 0x231850, //x           ? ? ? ? 81 53 48 F9 > child method
        onMessageAdded: 0xf3ee0, //x            ? ? ? ? ? ? ? ? 09 04 00 F9 ? ? ? ? 20 01 00 F9
        forceDisconnect: 0x24f5ec, //x          01 01 00 54 00 E2 84 52 ? ? ? ?
        onHardwareDataRequested: 0x6b04f8, //x   68 5E 00 39 69 42 00 39 60 02 80 3D
        addHealthBar: 0x2580d8, //x  ? ? ? ? F9 03 00 AA FA 0F 00 B9 20 06 00 B4

        invokeMenuActionHook: 0x23bc94, //x  B5 7E 40 92 94 7E 40 92 > and a call later
        writeJagString: 0xfc60c, //x A8 01 F8 B7 E9 02 80 52 22 01 08 CB
    },

    Client: {
        menu: 0x6f5f8, // 55 51 42 B9 14 69 69 F8 08 00 40 F9 E9 22 40 F9 08 05 40 F9 76 26 1B 9B 00 01 3F D6 08 0F 40 F9
        plane: 0x6b30, //
        currentWorld: 0x6ff4c, //x 2A 68 40 B9 ? ? ? ? 09 69 69 B8 08 68 40 B9
        gameState: 0x2130, //x   38 04 00 51 F3 03 00 AA F4 03 01 2A 15 00 08 8B
        loggedInCycle: 0x455524, //x E8 03 00 AA 00 04 40 F9
        cycle: 0x2154, //x 08 01 14 0B A8 06 00 B9 ? ? ? ? 29 55 61 B9 29 01 00 12 2A 01 14 0B
        serverTick: 0x2154 + 0x4, //x
        skillCurrentLevels: 0x6f434, //x 00 01 00 54 41 01 40 FD 01 3C A1 0E
        skillRealLevels: 0x6f498, //x
        skillExperiences: 0x6f4fc, //x
        localPlayer: 0x6f3c8, //x  01 0C 0B 29 02 10 0A 29
        worldView: 0x6b18, //x  57 1D 89 0B
        sceneTilesData: 0x6b28, //x 29 00 08 0B 3F CD 00 71 55 00 80 52
        scene: 0x70a28, //x F5 03 02 2A F3 03 01 AA F4 03 00 AA 08 69 69 F8 89 97 8A 52 08 01 09 8B
        destinationTile: 0x2008, //x ? ? ? ? 68 06 50 F9 75 0A 50 F9
        grandExchangeOffers: 0x6f7b0, //TODO 1F 1C 00 B9 1F 7D 00 A9 1F D1 00 F8 A8 02 40 F9
        hintArrowsDataContainer: 0x6ffc8, //x 09 3D 82 39 A9 00 F8 B7
        cameraX: 0x6f8d8, // 4A 7D 2D 9B 4C FD 7F D3 4A FD 66 93 4A 01 0C 0B 69 01 09 0B 29 01 0A 0B
        cameraZ: 0x6f8d8 + 0x4, //
        cameraY: 0x6f8d8 + 0x8, //
        cameraPitch: 0x6f8d8 + 0xc, //
        cameraYaw: 0x6f8d8 + 0x10, //
        cameraZoom: 0x6f790, //
        playableAreaX: 0x1fdc, //
        playableAreaY: 0x1fe0, //
    },

    HintArrowsDataContainer: {
        instance: 0x0,
    },

    HintArrowsDataContainerInstance: {
        hintArrowType: 0x0,
        hintArrowNpcIndex: 0x4,
        hintArrowX: 0x10,
        hintArrowY: 0x14,
    },

    WorldView: {
        sceneCoords: 0x10, //
        npcsHashMap: 0xb8, //
        playersHashMap: 0x88, //
    },

    Scene: {
        baseX: 0x54bc, //
        baseY: 0x54bc + 0x4,
        isInstanced: 0x54dd, //
    },
    SceneTile: {
        wallDecorationObject: 0x100, //
        groundObject: 0x110, //
        groundItems: 0xb0, //
        standardGameObject: 0x60, //
        wallObject: 0xf0, //
    },
    SceneTilesContainer: {
        tileAtZarr: 0x3b8, //         00 05 40 F9 40 00 00 B4
        tileAtXarr: 0x3a0, //         00 05 40 F9 40 00 00 B4
        tileAtYarr: 0x3a4, //   00 05 40 F9 40 00 00 B4

        collisionFlags: 0x68, //       ? ? ? ? 68 42 50 39 74 42 10 91 88 00 00 37     |   Chyba
    },

    SceneTilesData: {
        sceneTilesContainer: 0x10,
    },

    SceneCoords: {
        selectedX: 0x26c, //
        selectedY: 0x270, //
    },

    Actor: {
        rotation: 0x28,
        isMoving: 0x1b0 - 0x4,
        interactingIndex: 0x140,
        localX: 0x1ac, //
        localY: 0x1d4, //
        animation: 0x16c,
        animationFrame: 0x16c + 0x4,
        isRunningSpotanim: 0x2c,
        spotAnimation: 0x3a8,
        renderX: 0x20,
        renderY: 0x24,
    },
    SpotAnimation: {
        id: 0x4,
        frame: 0x14,
    },

    GroundObject: {
        tag: 0x40,
    },
    WallDecorationObject: {
        tag: 0x60,
    },
    StandardGameObject: {
        tag: 0x70,
    },
    WallObject: {
        tag: 0x58,
    },

    EngineSettings: {
        fpsCap: 0x185e0,
    },

    GrandExchangeOffer: {
        status: 0x0,
        itemId: 0x04,
        amount: 0x0c,
        price: 0x08,
        isBuying: 0x0c,
    },
    GroundItem: {
        id: 0x18,
        amount: 0x1c,
    },
    RsItemContainer: {
        id: 0x0,
        itemIds: 0x8,
        itemAmounts: 0x20,
    },
    Menu: {
        isOpen: 0x3a0, //
    },
    Npc: {
        composite: 0x3e0,
        index: 0x3f0,
        renderCycle: 0x34,
    },
    Player: {
        username: 0x3d0, //
        index: 0x488, //??
        headIconPrayer: 0x468, //
        headIconSkull: 0x3e8, //
        combatLevel: 0x438, //
    },

    NpcComposite: {
        id: 0x20,
    },

    Username: {
        value: 0x0,
    },
    Widget: {
        id: 0x24,
        index: 0x28,
        parentId: 0x94, //
        cycle: 0x58, //

        contentType: 0x50,
        text: 0x178, //x
        itemId: 0x788 + 0x20, //x  229-1, chyba 00231898
        width: 0x74, //
        height: 0x78, //
        relativeX: 0x7c, //
        relativeY: 0x80, //
        isHidden: 0x98, //
        textureId: 0xe8, //x
        isInput: 0x580, // x ??
        itemAmount: 0x78c + 0x20, //x  229-1, chyba 00231898
        children: 0x7a8 + 0x10, //x     13 01 F8 36 FD 7B 42 A9 F4 4F 41 A9 F6 57 C3 A8 C0 03 5F D6
    },

    ClientFingerprint: {
        libArchitecture: 0x0, //ARM64 or x86_64
        processorArchitecture: 0x18, //64 or (idk for arm)
        clientType: 0x50, //NXT-Android
        deviceInfo: 0x68,
    },
    DeviceInfo: {
        deviceName: 0x0, //Samsung / SM-G9500 (SM-G9500 / SM-G9500) (Android 12)
    },

    //Tego sie nie uzywa juz raczej chyba ze chcesz debugowac input.
    Native: {
        AKeyEvent_getKeyCode: 0x95f00,
        AInputEvent_getType: 0x95b70,
        AKeyEvent_getAction: 0x95000,
        AKeyEvent_getMetaState: 0x94a30,
        AInputEvent_getDeviceId: 0x95af0,

        AMotionEvent_getAction: 0x00095270,
        AMotionEvent_getPointerId: 0x000957f0,
        AMotionEvent_getX: 0x00094e20,
        AMotionEvent_getY: 0x00095200,
        AMotionEvent_getButtonState: 0x00094bf0,
        AMotionEvent_getPointerCount: 0x000943e0,
        AMotionEvent_getToolType: 0x00095540,
        AMotionEvent_getAxisValue: 0x000944b0,

        onInputEvent: 0xe4b48,

        keyEvent_Action: 0x14,
        keyEvent_Flags: 0x18,
        keyEvent_KeyCode: 0x1c,
        keyEvent_ScanCode: 0x20,
        keyEvent_MetaState: 0x24,
        keyEvent_DownTime: 0x28,
        keyEvent_EventTime: 0x2c,

        inputEvent_Source: 0xc,
        inputEvent_DeviceId: 0x30,
        inputEvent_Type: 0x10,
        keyEvent_RepeatCount: 0x28,
    },
}
